/**
 * Widget position and size
 */
export interface WidgetPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  isResizable?: boolean;
  isDraggable?: boolean;
}

/**
 * Widget metadata
 */
export interface WidgetMeta {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  tags?: string[];
  version: string;
  author?: string;
  settingsSchema?: Record<string, any>;
  defaultSettings?: Record<string, any>;
}

/**
 * Widget state
 */
export interface WidgetState {
  isActive: boolean;
  isMinimized: boolean;
  isMaximized: boolean;
  isFullscreen: boolean;
  zIndex: number;
  lastUpdated: number;
  settings: Record<string, any>;
}

/**
 * Widget data
 */
export interface WidgetData<T = any> {
  content: T;
  lastFetched?: number;
  error?: string | null;
  loading?: boolean;
}

/**
 * Widget definition
 */
export interface WidgetDefinition<T = any> {
  meta: WidgetMeta;
  position: WidgetPosition;
  state: WidgetState;
  data: WidgetData<T>;
  component: React.ComponentType<WidgetProps<T>>;
  settingsPanel?: React.ComponentType<{ widget: WidgetDefinition<T>; onSettingsChange: (settings: any) => void }>;
  onSettingsChange?: (settings: any) => void;
}

/**
 * Widget props
 */
export interface WidgetProps<T = any> {
  widget: WidgetDefinition<T>;
  onUpdate: (updates: Partial<WidgetDefinition<T>>) => void;
  onRemove: () => void;
  onSettingsChange: (settings: any) => void;
  isEditing: boolean;
}

/**
 * Widget context type
 */
export interface WidgetContextType {
  widgets: Record<string, WidgetDefinition>;
  activeWidgetId: string | null;
  addWidget: (widget: WidgetDefinition) => string;
  removeWidget: (id: string) => void;
  updateWidget: (id: string, updates: Partial<WidgetDefinition>) => void;
  setActiveWidget: (id: string | null) => void;
  bringToFront: (id: string) => void;
  saveLayout: () => void;
  loadLayout: () => void;
  resetLayout: () => void;
}

/**
 * OpenRouter API types
 */
export interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
  stop?: string[];
  stream?: boolean;
  transform?: (data: any) => any;
}

export interface OpenRouterResponse {
  id: string;
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
    index: number;
  }>;
  created: number;
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface WidgetUpdateEvent {
  widgetId: string;
  updates: Partial<WidgetDefinition>;
}

export interface WidgetMessage {
  type: 'update' | 'add' | 'remove' | 'minimize' | 'maximize' | 'close' | 'settings' | 'data';
  payload: any;
}
