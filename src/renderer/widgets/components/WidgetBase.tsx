import React, { useRef, useCallback, useState, useEffect } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Resizable } from 're-resizable';
import { WidgetProps } from '../types/widgets';
import { useWidget } from '../hooks/useWidget';
import { cn } from '../../lib';

interface WidgetBaseProps extends WidgetProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showControls?: boolean;
  onDragStop?: (position: { x: number; y: number }) => void;
  onResizeStop?: (size: { width: number; height: number }) => void;
}

const WidgetBase: React.FC<WidgetBaseProps> = ({
  widget,
  onUpdate,
  onRemove,
  onSettingsChange,
  isEditing,
  children,
  className,
  showHeader = true,
  showControls = true,
  onDragStop,
  onResizeStop,
}) => {
  const { isActive, toggleMinimize, toggleMaximize, closeWidget, focusWidget } = useWidget(widget.meta.id);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: widget.position.x, y: widget.position.y });
  const [size, setSize] = useState({ width: widget.position.width, height: widget.position.height });
  const widgetRef = useRef<HTMLDivElement>(null);

  // Update position when widget prop changes
  useEffect(() => {
    setPosition({ x: widget.position.x, y: widget.position.y });
  }, [widget.position.x, widget.position.y]);

  // Update size when widget prop changes
  useEffect(() => {
    setSize({ width: widget.position.width, height: widget.position.height });
  }, [widget.position.width, widget.position.height]);

  // Handle drag and drop
  const [{ isDragging: isDragActive }, drag] = useDrag(
    () => ({
      type: 'WIDGET',
      item: { id: widget.meta.id },
      collect: (monitor) => ({
        isDragging: !!monitor.isDragging(),
      }),
    }),
    [widget.meta.id]
  );

  const [, drop] = useDrop(
    () => ({
      accept: 'WIDGET',
      hover(item: { id: string; type: string }, monitor) {
        if (!widgetRef.current) return;

        const dragId = item.id;
        const hoverId = widget.meta.id;

        // Don't replace items with themselves
        if (dragId === hoverId) return;

        // Determine rectangle on screen
        const hoverBoundingRect = widgetRef.current.getBoundingClientRect();

        // Get vertical middle
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

        // Determine mouse position
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) return;

        // Get pixels to the top
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;

        // Only perform the move when the mouse has crossed half of the items height
        // When dragging downwards, only move when the cursor is below 50%
        // When dragging upwards, only move when the cursor is above 50%
        // Dragging downwards
        if (dragId !== hoverId && hoverClientY < hoverMiddleY) {
          // Reorder logic would go here
        }
      },
    }),
    [widget.meta.id]
  );

  // Handle drag stop
  const handleDragStop = useCallback(
    (e: any, data: { x: number; y: number }) => {
      setPosition({ x: data.x, y: data.y });
      onUpdate({
        position: {
          ...widget.position,
          x: data.x,
          y: data.y,
        },
      });
      onDragStop?.({ x: data.x, y: data.y });
    },
    [onUpdate, onDragStop, widget.position]
  );

  // Handle resize stop
  const handleResizeStop = useCallback(
    (e: any, direction: any, ref: any, d: any) => {
      const newWidth = size.width + d.width;
      const newHeight = size.height + d.height;

      setSize({ width: newWidth, height: newHeight });

      onUpdate({
        position: {
          ...widget.position,
          width: newWidth,
          height: newHeight,
        },
      });

      onResizeStop?.({ width: newWidth, height: newHeight });
    },
    [size, onUpdate, onResizeStop, widget.position]
  );

  // Handle widget click to bring to front
  const handleWidgetClick = useCallback(() => {
    if (!isActive) {
      focusWidget();
    }
  }, [isActive, focusWidget]);

  // Don't render if widget is minimized
  if (widget.state.isMinimized) {
    return (
      <div
        ref={(node) => drag(drop(node))}
        className={cn(
          'fixed bg-gray-800 bg-opacity-90 text-white p-2 rounded-md shadow-lg cursor-move z-50',
          'flex items-center space-x-2',
          isActive ? 'ring-2 ring-blue-500' : ''
        )}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          opacity: isDragging ? 0.5 : 1,
        }}
        onClick={handleWidgetClick}
        onDoubleClick={toggleMinimize}
      >
        {widget.meta.icon && (
          <div className="w-4 h-4 flex items-center justify-center">
            {widget.meta.icon}
          </div>
        )}
        <span className="text-sm font-medium truncate max-w-xs">
          {widget.meta.title}
        </span>
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleMinimize();
          }}
          className="ml-2 text-gray-400 hover:text-white"
        >
          <span>↩️</span>
        </button>
      </div>
    );
  }

  return (
    <div
      onClick={handleWidgetClick}
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: widget.state.zIndex,
      }}
    >
      <Resizable
        ref={(node) => {
          if (node) {
            widgetRef.current = node.resizable;
            drag(drop(node.resizable));
          }
        }}
        size={size}
        onResizeStop={handleResizeStop}
        minWidth={widget.position.minWidth || 200}
        minHeight={widget.position.minHeight || 150}
        maxWidth={widget.position.maxWidth || '100%'}
        maxHeight={widget.position.maxHeight || '100%'}
        enable={{
          top: isEditing,
          right: isEditing,
          bottom: isEditing,
          left: isEditing,
          topRight: isEditing,
          bottomRight: isEditing,
          bottomLeft: isEditing,
          topLeft: isEditing,
        }}
        className={cn(
          'bg-gray-900 bg-opacity-90 backdrop-blur-sm rounded-lg shadow-xl overflow-hidden flex flex-col',
          'border border-gray-700',
          isActive ? 'ring-2 ring-blue-500' : '',
          className
        )}
        style={{
          opacity: isDragging ? 0.8 : 1,
          transition: 'opacity 0.2s',
        }}
      >
      {showHeader && (
        <div
          className={cn(
            'px-4 py-2 flex items-center justify-between border-b border-gray-800',
            'bg-gray-800 bg-opacity-50 cursor-move select-none',
            isEditing ? 'cursor-move' : 'cursor-default'
          )}
          onDoubleClick={toggleMaximize}
        >
          <div className="flex items-center space-x-2">
            {widget.meta.icon && (
              <div className="w-4 h-4 flex items-center justify-center">
                {widget.meta.icon}
              </div>
            )}
            <h3 className="text-sm font-medium text-gray-200 truncate">
              {widget.meta.title}
            </h3>
          </div>

          {showControls && (
            <div className="flex items-center space-x-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleMinimize();
                }}
                className="p-1 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white"
                aria-label="Minimize"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleMaximize();
                }}
                className="p-1 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white"
                aria-label={widget.state.isMaximized ? 'Restore' : 'Maximize'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                  {widget.state.isMaximized ? (
                    <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                  ) : (
                    <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm2 0h10v10H5V5z" clipRule="evenodd" />
                  )}
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeWidget();
                }}
                className="p-1 rounded-full text-gray-400 hover:bg-red-500 hover:text-white"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          )}
        </div>
      )}

      <div className="flex-1 overflow-auto p-4">
        {children}
      </div>

      {widget.data?.loading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {widget.data?.error && (
        <div className="bg-red-900 bg-opacity-50 text-red-100 text-xs p-2">
          {widget.data.error}
        </div>
      )}
    </Resizable>
    </div>
  );
};

export default WidgetBase;
