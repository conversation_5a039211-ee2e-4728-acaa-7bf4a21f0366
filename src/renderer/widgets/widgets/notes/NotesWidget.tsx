import React, { useState, useEffect } from 'react';
import { WidgetProps } from '../../types/widgets';
import WidgetBase from '../../components/WidgetBase';
import { useWidget } from '../../hooks/useWidget';

interface NotesWidgetData {
  content: string;
  title: string;
  lastUpdated: number;
}

const NotesWidget: React.FC<WidgetProps> = (props) => {
  const { widget, onUpdate, onSettingsChange } = props;
  const { updateWidgetData, generateContent } = useWidget(widget.meta.id);
  const [notes, setNotes] = useState<NotesWidgetData>(
    widget.data?.content || { content: '', title: 'Untitled Note', lastUpdated: Date.now() }
  );
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when widget data changes
  useEffect(() => {
    if (widget.data?.content) {
      setNotes(widget.data.content);
    }
  }, [widget.data?.content]);

  // Save notes after a delay when content changes
  useEffect(() => {
    if (!isEditing) return;

    const timer = setTimeout(() => {
      const updatedNotes = {
        ...notes,
        lastUpdated: Date.now(),
      };

      updateWidgetData<NotesWidgetData>(
        () => updatedNotes,
        { loading: false }
      );
    }, 1000);

    return () => clearTimeout(timer);
  }, [notes, isEditing, updateWidgetData]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotes(prev => ({
      ...prev,
      title: e.target.value
    }));
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNotes(prev => ({
      ...prev,
      content: e.target.value
    }));
  };

  const handleEnhanceWithAI = async () => {
    try {
      await updateWidgetData<NotesWidgetData>(
        async (current) => {
          const enhancedContent = await generateContent(
            `Improve and enhance the following notes. Make them more detailed and well-structured:\n\n${current.content}`,
            { systemPrompt: 'You are a helpful writing assistant that improves and enhances notes.' }
          );

          return {
            ...current,
            content: enhancedContent || current.content,
            lastUpdated: Date.now(),
          };
        },
        { loading: true }
      );
    } catch (error) {
      console.error('Failed to enhance notes with AI:', error);
    }
  };

  return (
    <WidgetBase
      {...props}
      className="flex flex-col"
    >
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="p-4 border-b border-gray-700">
          <input
            type="text"
            value={notes.title}
            onChange={handleTitleChange}
            onFocus={() => setIsEditing(true)}
            onBlur={() => setIsEditing(false)}
            className="w-full bg-transparent text-lg font-medium text-white outline-none"
            placeholder="Note Title"
          />
        </div>

        <div className="flex-1 p-4 overflow-auto">
          <textarea
            value={notes.content}
            onChange={handleContentChange}
            onFocus={() => setIsEditing(true)}
            onBlur={() => setIsEditing(false)}
            className="w-full h-full bg-transparent text-gray-200 outline-none resize-none"
            placeholder="Start writing your notes here..."
          />
        </div>

        <div className="p-2 border-t border-gray-700 bg-gray-800 bg-opacity-50 flex justify-between items-center">
          <div className="text-xs text-gray-400">
            Last updated: {new Date(notes.lastUpdated).toLocaleString()}
          </div>
          <button
            onClick={handleEnhanceWithAI}
            className="px-3 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center space-x-1"
            title="Enhance with AI"
          >
            <span>✨</span>
            <span>Enhance</span>
          </button>
        </div>
      </div>
    </WidgetBase>
  );
};

// Register the widget
export const registerNotesWidget = () => {
  // Import dynamically to avoid circular dependencies
  import('../../utils/widgetRegistry').then(({ registerWidget }) => {
    registerWidget('notes', {
      meta: {
        title: 'Notes',
        description: 'A simple note-taking widget with AI enhancement',
        icon: '📝',
        version: '1.0.0',
        author: 'Cerebras OS',
        tags: ['productivity', 'notes', 'ai'],
      },
      defaultPosition: {
        width: 350,
        height: 400,
        minWidth: 250,
        minHeight: 300,
      },
      defaultState: {
        settings: {
          fontSize: '14px',
          fontFamily: 'sans-serif',
          theme: 'dark',
        },
      },
      defaultData: {
        content: {
          title: 'Untitled Note',
          content: '',
          lastUpdated: Date.now(),
        },
      },
      component: NotesWidget,
    });
  });
};

export default NotesWidget;
