import React, { createContext, useContext, useReducer, useCallback, useMemo } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { WidgetContextType, WidgetDefinition } from '../types/widgets';

// Initial state
const initialState: {
  widgets: Record<string, WidgetDefinition>;
  activeWidgetId: string | null;
} = {
  widgets: {},
  activeWidgetId: null,
};

type Action =
  | { type: 'ADD_WIDGET'; payload: WidgetDefinition }
  | { type: 'REMOVE_WIDGET'; payload: string }
  | { type: 'UPDATE_WIDGET'; payload: { id: string; updates: Partial<WidgetDefinition> } }
  | { type: 'SET_ACTIVE_WIDGET'; payload: string | null }
  | { type: 'BRING_TO_FRONT'; payload: string }
  | { type: 'LOAD_LAYOUT'; payload: Record<string, WidgetDefinition> };

function widgetReducer(
  state: typeof initialState,
  action: Action
): typeof initialState {
  switch (action.type) {
    case 'ADD_WIDGET':
      return {
        ...state,
        widgets: {
          ...state.widgets,
          [action.payload.meta.id]: action.payload,
        },
        activeWidgetId: action.payload.meta.id,
      };

    case 'REMOVE_WIDGET': {
      const newWidgets = { ...state.widgets };
      delete newWidgets[action.payload];
      return {
        ...state,
        widgets: newWidgets,
        activeWidgetId: state.activeWidgetId === action.payload ? null : state.activeWidgetId,
      };
    }

    case 'UPDATE_WIDGET': {
      const { id, updates } = action.payload;
      const widget = state.widgets[id];
      if (!widget) return state;

      return {
        ...state,
        widgets: {
          ...state.widgets,
          [id]: {
            ...widget,
            ...updates,
            state: {
              ...widget.state,
              ...(updates.state || {}),
              lastUpdated: Date.now(),
            },
          },
        },
      };
    }

    case 'SET_ACTIVE_WIDGET':
      return {
        ...state,
        activeWidgetId: action.payload,
      };

    case 'BRING_TO_FRONT': {
      const widget = state.widgets[action.payload];
      if (!widget) return state;

      // Find the highest z-index and increment it
      const maxZIndex = Object.values(state.widgets).reduce(
        (max, w) => Math.max(max, w.state.zIndex),
        0
      );

      return {
        ...state,
        widgets: {
          ...state.widgets,
          [action.payload]: {
            ...widget,
            state: {
              ...widget.state,
              zIndex: maxZIndex + 1,
            },
          },
        },
      };
    }


    case 'LOAD_LAYOUT':
      return {
        ...state,
        widgets: action.payload,
      };

    default:
      return state;
  }
}

// Create context
const WidgetContext = createContext<WidgetContextType | undefined>(undefined);

// Provider component
export const WidgetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(widgetReducer, initialState);

  const addWidget = useCallback((widget: WidgetDefinition) => {
    const id = uuidv4();
    const maxZIndex = Math.max(0, ...Object.values(state.widgets).map(w => w.state.zIndex));

    const newWidget: WidgetDefinition = {
      ...widget,
      meta: {
        ...widget.meta,
        id,
      },
      state: {
        ...widget.state,
        isActive: true,
        isMinimized: false,
        isMaximized: false,
        isFullscreen: false,
        zIndex: maxZIndex + 1,
        lastUpdated: Date.now(),
        settings: {
          ...(widget.meta.defaultSettings || {}),
          ...(widget.state?.settings || {}),
        },
      },
    };

    dispatch({ type: 'ADD_WIDGET', payload: newWidget });
    return id;
  }, [state.widgets]);

  const removeWidget = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_WIDGET', payload: id });
  }, []);

  const updateWidget = useCallback((id: string, updates: Partial<WidgetDefinition>) => {
    dispatch({ type: 'UPDATE_WIDGET', payload: { id, updates } });
  }, []);

  const setActiveWidget = useCallback((id: string | null) => {
    dispatch({ type: 'SET_ACTIVE_WIDGET', payload: id });
  }, []);

  const bringToFront = useCallback((id: string) => {
    dispatch({ type: 'BRING_TO_FRONT', payload: id });
  }, []);

  const saveLayout = useCallback(() => {
    try {
      localStorage.setItem('widgets-layout', JSON.stringify(state.widgets));
    } catch (error) {
      console.error('Failed to save layout:', error);
    }
  }, [state.widgets]);

  const loadLayout = useCallback(() => {
    try {
      const savedLayout = localStorage.getItem('widgets-layout');
      if (savedLayout) {
        const parsed = JSON.parse(savedLayout);
        dispatch({ type: 'LOAD_LAYOUT', payload: parsed });
      }
    } catch (error) {
      console.error('Failed to load layout:', error);
    }
  }, []);

  const resetLayout = useCallback(() => {
    // Keep only the default widgets or clear all
    dispatch({ type: 'LOAD_LAYOUT', payload: {} });
  }, []);

  const value = useMemo(
    () => ({
      widgets: state.widgets,
      activeWidgetId: state.activeWidgetId,
      addWidget,
      removeWidget,
      updateWidget,
      setActiveWidget,
      bringToFront,
      saveLayout,
      loadLayout,
      resetLayout,
    }),
    [
      state.widgets,
      state.activeWidgetId,
      addWidget,
      removeWidget,
      updateWidget,
      setActiveWidget,
      bringToFront,
      saveLayout,
      loadLayout,
      resetLayout,
    ]
  );

  return <WidgetContext.Provider value={value}>{children}</WidgetContext.Provider>;
};

// Custom hook to use the widget context
export const useWidgets = (): WidgetContextType => {
  const context = useContext(WidgetContext);
  if (context === undefined) {
    throw new Error('useWidgets must be used within a WidgetProvider');
  }
  return context;
};

export default WidgetContext;
