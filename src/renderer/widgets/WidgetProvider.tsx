import React, { useEffect, useMemo } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { WidgetProvider as WidgetContextProvider, useWidgets } from './context/WidgetContext';
import { WidgetDefinition } from './types/widgets';
import { createWidget } from './utils/widgetRegistry';

// Import widget components to register them
import { registerNotesWidget } from './widgets/notes/NotesWidget';

// Register default widgets
registerNotesWidget();

interface WidgetProviderProps {
  children: React.ReactNode;
  initialWidgets?: WidgetDefinition[];
  onWidgetsChange?: (widgets: Record<string, WidgetDefinition>) => void;
}

const WidgetProvider: React.FC<WidgetProviderProps> = ({
  children,
  initialWidgets = [],
  onWidgetsChange,
}) => {
  // Create initial widgets if none are provided
  const defaultWidgets = useMemo(() => {
    return initialWidgets.length > 0
      ? initialWidgets
      : [
          createWidget('notes', {
            position: { x: 100, y: 100 },
            state: { zIndex: 1 },
          }),
        ].filter(Boolean) as WidgetDefinition[];
  }, [initialWidgets]);

  return (
    <DndProvider backend={HTML5Backend}>
      <WidgetContextProvider>
        <WidgetManager
          defaultWidgets={defaultWidgets}
          onWidgetsChange={onWidgetsChange}
        >
          {children}
        </WidgetManager>
      </WidgetContextProvider>
    </DndProvider>
  );
};

// WidgetManager handles the actual rendering of widgets
const WidgetManager: React.FC<{
  children: React.ReactNode;
  defaultWidgets: WidgetDefinition[];
  onWidgetsChange?: (widgets: Record<string, WidgetDefinition>) => void;
}> = ({ children, defaultWidgets, onWidgetsChange }) => {
  const {
    widgets,
    addWidget,
  } = useWidgets();

  // Initialize with default widgets if none exist
  useEffect(() => {
    if (Object.keys(widgets).length === 0 && defaultWidgets.length > 0) {
      defaultWidgets.forEach(widget => {
        if (widget) {
          addWidget(widget);
        }
      });
    }
  }, [addWidget, defaultWidgets, widgets]);

  // Notify parent when widgets change
  useEffect(() => {
    if (onWidgetsChange) {
      onWidgetsChange(widgets);
    }
  }, [widgets, onWidgetsChange]);



  return (
    <>
      {children}
    </>
  );
};

export default WidgetProvider;
