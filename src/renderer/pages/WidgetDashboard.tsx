import React, { useCallback } from 'react';
import { useWidgetSystem } from '../widgets';
import { Plus } from 'lucide-react';

interface WidgetDashboardProps {}

const WidgetDashboard: React.FC<WidgetDashboardProps> = () => {
  const {
    addWidget,
    widgets,
    resetLayout,
    updateWidget,
    removeWidget
  } = useWidgetSystem();

  const handleAddNotesWidget = useCallback(() => {
    const widgetCount = Object.keys(widgets).length;
    const row = Math.floor(widgetCount / 3);
    const col = widgetCount % 3;

    addWidget('notes', {
      position: {
        x: 100 + (col * 350),
        y: 100 + (row * 300),
        width: 300,
        height: 200,
        minWidth: 200,
        minHeight: 150,
        isResizable: true,
        isDraggable: true,
      },
    });
  }, [addWidget, widgets]);

  return (
    <div className="flex flex-col h-full p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Widget Dashboard</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleAddNotesWidget}
            className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Plus className="h-4 w-4" />
            <span>Add Notes Widget</span>
          </button>

          <button
            onClick={resetLayout}
            className="px-3 py-1.5 text-sm font-medium rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Reset Layout
          </button>
        </div>
      </div>

      <div className="flex-1 relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
        {Object.entries(widgets).map(([id, widget]) => {
          const WidgetComponent = widget.component;
          return (
            <WidgetComponent
              key={id}
              widget={widget}
              onUpdate={(updates) => {
                updateWidget(id, updates);
              }}
              onRemove={() => {
                removeWidget(id);
              }}
              onSettingsChange={(settings) => {
                updateWidget(id, {
                  state: {
                    ...widget.state,
                    settings: {
                      ...(widget.state.settings || {}),
                      ...settings,
                    },
                  },
                });
              }}
              isEditing={widget.state.isActive}
            />
          );
        })}
      </div>

      <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
        <p>Total Widgets: {Object.keys(widgets).length}</p>
      </div>
    </div>
  );
};

export default WidgetDashboard;
