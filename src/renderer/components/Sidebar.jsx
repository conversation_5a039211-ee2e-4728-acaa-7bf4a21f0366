import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, MessageCircle, Menu, Trash2, BookOpen, BrainCircuit, Package, Zap, Sparkles, Hammer, Palette, Code, Terminal, Brush, Layers, LayoutDashboard } from 'lucide-react';
import { useChat } from '../context/ChatContext';

const navItems = [
    { to: '/', label: 'Home', icon: <Home size={20} /> },
    { to: '/chat', label: 'Chat', icon: <MessageCircle size={20} /> },
    { to: '/multidialog', label: 'Multi-Dialog', icon: <BrainCircuit size={20} /> },
    { to: '/management', label: 'Management', icon: <BookOpen size={20} /> },
    { to: '/components', label: 'Component Viewer', icon: <Package size={20} /> },
    { to: '/provider-settings', label: 'Provider Settings', icon: <Package size={20} /> },
    { to: '/pica-playground', label: 'PICA PLAYGROUND', icon: <Zap size={20} /> },
    { to: '/cuicui', label: 'Cuicui', icon: <Sparkles size={20} /> },
    { to: '/inja-builder', label: 'ALIAS Turbo Builder', icon: <Hammer size={20} /> },
    { to: '/theme-editor', label: 'ALIAS HQ Theme Editor', icon: <Palette size={20} /> },
    { to: '/nextui-theme', label: 'NextUI Theme', icon: <Brush size={20} /> },
    { to: '/bmad-method', label: 'BMAD Method', icon: <Sparkles size={20} /> },
    { to: '/cult-ui', label: 'Cult UI', icon: <Layers size={20} /> },
    { to: '/widget-dashboard', label: 'Widget Dashboard', icon: <LayoutDashboard size={20} /> },
    { to: '/dev-tools', label: 'Dev Tools', icon: <Code size={20} /> },
    { to: '/terminal', label: 'Terminal', icon: <Terminal size={20} /> }
];

const Sidebar = ({ collapsed, onToggle }) => {
    const location = useLocation();
    const { recentChats, setActiveChatId, activeChatId, deleteChat } = useChat();
    const [showAll, setShowAll] = useState(false);
    const [confirmDeleteId, setConfirmDeleteId] = useState(null);
    const chatsToShow = showAll ? recentChats : recentChats.slice(0, 5);

    useEffect(() => {
        localStorage.setItem('sidebar-collapsed', collapsed ? 'true' : 'false');
    }, [collapsed]);
    return (
        <nav
            className={`h-full flex flex-col transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'} min-w-[4rem] bg-black border-r border-white/5`}
            aria-label="Main navigation"
            role="navigation"
        >
            <div className="flex items-center justify-between px-4 py-4 border-b border-white/5">
                <span className={`font-bold text-xl bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent transition-all duration-300 ${collapsed ? 'hidden' : 'block'}`}>ALIAS</span>
                <button
                    className="p-2 rounded-lg hover:bg-white/[0.02] transition-all duration-300 hover:scale-105"
                    onClick={onToggle}
                    aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
                >
                    <Menu size={20} className="text-gray-300" />
                </button>
            </div>
            <ul className="flex-1 py-4 px-3 space-y-2" role="list">
                {navItems.map((item, idx) => (
                    <li key={item.to} role="listitem">
                        <Link
                            to={item.to}
                            className={`group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium relative overflow-hidden ${
                                location.pathname === item.to
                                    ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-400 shadow-lg shadow-purple-500/10'
                                    : 'text-gray-400 hover:bg-white/[0.02] hover:text-gray-200'
                            }`}
                            aria-current={location.pathname === item.to ? 'page' : undefined}
                            tabIndex={0}
                            role="menuitem"
                            onKeyDown={e => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.currentTarget.click();
                                }
                                // Navegación con flechas
                                if (e.key === 'ArrowDown') {
                                    const next = e.currentTarget.parentElement.nextElementSibling?.querySelector('a');
                                    if (next) next.focus();
                                }
                                if (e.key === 'ArrowUp') {
                                    const prev = e.currentTarget.parentElement.previousElementSibling?.querySelector('a');
                                    if (prev) prev.focus();
                                }
                            }}
                        >
                            {location.pathname === item.to && (
                                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-transparent animate-pulse" />
                            )}
                            <span className={`relative z-10 transition-transform duration-300 ${location.pathname === item.to ? 'scale-110' : 'group-hover:scale-110'}`}>
                                {item.icon}
                            </span>
                            <span className={`relative z-10 ${collapsed ? 'hidden' : 'inline'} transition-all duration-300`}>{item.label}</span>
                        </Link>
                    </li>
                ))}
            </ul>
            {/* Recent Chats */}
            <div className={`px-3 pb-4 ${collapsed ? 'hidden' : ''} border-t border-white/5`}>
                <div className="text-xs text-gray-400 mb-3 mt-4 px-2 uppercase tracking-wider font-semibold">Recent Chats</div>
                <ul className="space-y-2">
                    {chatsToShow.map(chat => (
                        <li key={chat.id} className="group relative">
                            <div
                                role="button"
                                tabIndex={0}
                                className={`w-full text-left px-3 py-2.5 rounded-lg transition-all duration-300 flex items-center justify-between cursor-pointer group/chat ${
                                    activeChatId === chat.id
                                        ? 'bg-white/5 text-white shadow-md'
                                        : 'text-gray-400 hover:bg-white/[0.02] hover:text-gray-200'
                                }`}
                                onClick={() => setActiveChatId(chat.id)}
                                onKeyDown={e => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        setActiveChatId(chat.id);
                                    }
                                }}
                                title={chat.title}
                            >
                                <div className="flex-1 overflow-hidden">
                                    <span className="truncate block max-w-[140px] font-medium text-sm">{chat.title || 'Untitled Chat'}</span>
                                    <span className="block text-xs opacity-60 truncate max-w-[140px] mt-0.5">
                                        {chat.messages?.[chat.messages.length - 1]?.content?.slice(0, 30) || 'No messages'}
                                    </span>
                                </div>
                                <button
                                    className="ml-2 p-1.5 rounded-lg hover:bg-red-500/20 text-gray-400 hover:text-red-400 opacity-0 group-hover/chat:opacity-100 transition-all duration-300 flex-shrink-0"
                                    tabIndex={0}
                                    onClick={e => { e.stopPropagation(); setConfirmDeleteId(chat.id); }}
                                    title="Delete chat"
                                    aria-label={`Delete chat: ${chat.title || 'Untitled Chat'}`}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </div>
                            {confirmDeleteId === chat.id && (
                                <div className="absolute left-0 top-0 z-50 bg-black/90 backdrop-blur-xl border border-white/10 rounded-lg shadow-2xl p-4 flex flex-col gap-3 w-64 animate-scaleIn">
                                    <span className="text-sm text-gray-200">Are you sure you want to delete this chat?</span>
                                    <div className="flex gap-2 justify-end">
                                        <button
                                            className="px-3 py-1.5 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300"
                                            onClick={() => setConfirmDeleteId(null)}
                                        >
                                            Cancel
                                        </button>
                                        <button
                                            className="px-3 py-1.5 rounded-lg bg-red-500 text-white hover:bg-red-600 transition-all duration-300"
                                            onClick={() => { deleteChat(chat.id); setConfirmDeleteId(null); }}
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            )}
                        </li>
                    ))}
                </ul>
                {recentChats.length > 5 && (
                    <button
                        className="w-full mt-3 text-xs text-purple-400 hover:text-purple-300 transition-colors duration-300 font-medium"
                        onClick={() => setShowAll(v => !v)}
                    >
                        {showAll ? '← Show less' : 'Load more →'}
                    </button>
                )}
            </div>
        </nav>
    );
};

export default Sidebar;